<template>
  <div class="bg-base-200/40 rounded-lg p-6">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold">Invoices</h3>
      <button 
        @click="refreshInvoices"
        :disabled="isLoading"
        class="btn btn-outline btn-sm"
      >
        <svg v-if="!isLoading" class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
        <span v-if="isLoading" class="loading loading-spinner loading-xs mr-2"></span>
        Refresh
      </button>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading && invoices.length === 0" class="text-center py-8">
      <span class="loading loading-spinner loading-md"></span>
      <p class="text-base-content/70 mt-2">Loading invoices...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="text-center py-8">
      <div class="text-error mb-2">
        <svg class="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      </div>
      <p class="text-error font-medium">Failed to load invoices</p>
      <p class="text-base-content/70 text-sm mt-1">{{ error }}</p>
      <button @click="loadInvoices" class="btn btn-outline btn-sm mt-3">
        Try again
      </button>
    </div>

    <!-- Empty State -->
    <div v-else-if="invoices.length === 0" class="text-center py-8">
      <div class="text-base-content/50 mb-2">
        <svg class="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      </div>
      <p class="text-base-content/70 font-medium">No invoices yet</p>
      <p class="text-base-content/50 text-sm">Invoices will appear here after successful payments</p>
    </div>

    <!-- Invoice List -->
    <div v-else class="space-y-3">
      <div 
        v-for="invoice in invoices" 
        :key="invoice.id"
        class="flex items-center justify-between p-4 bg-base-100 rounded-lg border border-base-300 hover:border-primary/30 transition-colors"
      >
        <div class="flex-1">
          <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
              <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div class="flex-1 min-w-0">
              <div class="flex items-center space-x-2">
                <p class="font-medium text-base-content">{{ invoice.invoiceNumber }}</p>
                <span class="badge badge-success badge-sm">Paid</span>
              </div>
              <p class="text-sm text-base-content/70 truncate">{{ invoice.description }}</p>
              <div class="flex items-center space-x-4 mt-1 text-xs text-base-content/50">
                <span>{{ formatDate(invoice.generatedAt) }}</span>
                <span v-if="invoice.billingPeriod">{{ invoice.billingPeriod }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="flex items-center space-x-3">
          <div class="text-right">
            <p class="font-semibold text-base-content">{{ formatCurrency(invoice.amount, invoice.currency) }}</p>
          </div>
          <button 
            @click="downloadInvoice(invoice.id, invoice.invoiceNumber)"
            :disabled="downloadingInvoices.has(invoice.id)"
            class="btn btn-outline btn-sm"
            :class="{ 'loading': downloadingInvoices.has(invoice.id) }"
          >
            <svg v-if="!downloadingInvoices.has(invoice.id)" class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Download
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

interface Invoice {
  id: string
  invoiceNumber: string
  amount: number
  currency: string
  description: string
  billingPeriod?: string
  generatedAt: string
  paymentId: string
  paidAt?: string
}

// State
const invoices = ref<Invoice[]>([])
const isLoading = ref(false)
const error = ref<string | null>(null)
const downloadingInvoices = ref(new Set<string>())

// Methods
const loadInvoices = async () => {
  try {
    isLoading.value = true
    error.value = null

    console.log('Loading invoices from /api/invoices...')

    // Add timeout to prevent hanging requests
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 10000) // 10 second timeout

    const response = await fetch('/api/invoices', {
      credentials: 'include',
      signal: controller.signal
    })

    clearTimeout(timeoutId)

    console.log('Invoice API response status:', response.status)

    if (!response.ok) {
      const errorText = await response.text()
      console.error('Invoice API error response:', errorText)
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    console.log('Invoice API response data:', data)

    if (data.success) {
      invoices.value = data.invoices || []
      console.log(`Loaded ${invoices.value.length} invoices`)
    } else {
      throw new Error(data.message || 'Invalid response format')
    }
  } catch (err: any) {
    if (err.name === 'AbortError') {
      error.value = 'Request timed out. Please check your connection and try again.'
      console.error('Invoice loading timed out')
    } else {
      error.value = err.message
      console.error('Failed to load invoices:', err)
    }
  } finally {
    isLoading.value = false
  }
}

const refreshInvoices = async () => {
  await loadInvoices()
}

const downloadInvoice = async (invoiceId: string, invoiceNumber: string) => {
  try {
    downloadingInvoices.value.add(invoiceId)
    
    const response = await fetch(`/api/invoices/${invoiceId}/download`, {
      credentials: 'include'
    })
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    // Create blob and download
    const blob = await response.blob()
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${invoiceNumber}.pdf`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
  } catch (err: any) {
    console.error('Failed to download invoice:', err)
    alert('Failed to download invoice. Please try again.')
  } finally {
    downloadingInvoices.value.delete(invoiceId)
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const formatCurrency = (amount: number, currency: string) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency.toUpperCase()
  }).format(amount)
}

onMounted(() => {
  loadInvoices()
})
</script>
