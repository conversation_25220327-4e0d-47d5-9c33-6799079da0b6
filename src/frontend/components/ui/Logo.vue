<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'
import logoLight from '@/assets/images/logo-light.png'
import logoDark from '@/assets/images/logo-dark.png'

interface Props {
  size?: 'sm' | 'md' | 'lg'
  showText?: boolean
  clickable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  showText: true,
  clickable: true
})

const sizeClasses = {
  sm: 'h-6',
  md: 'h-8',
  lg: 'h-12'
}

// Reactive theme state
const currentTheme = ref('emerald')

// Determine which logo to use based on theme
const logoSrc = computed(() => {
  return currentTheme.value === 'dim' ? logoDark : logoLight
})

// Function to update theme from DOM
const updateTheme = () => {
  if (typeof document !== 'undefined') {
    const theme = document.documentElement.getAttribute('data-theme') || 'emerald'
    currentTheme.value = theme
  }
}

// MutationObserver to watch for theme changes
let observer: MutationObserver | null = null

onMounted(() => {
  // Initial theme check
  updateTheme()

  // Watch for changes to data-theme attribute
  if (typeof document !== 'undefined') {
    observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'data-theme') {
          updateTheme()
        }
      })
    })

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['data-theme']
    })
  }
})

onUnmounted(() => {
  observer?.disconnect()
})
</script>

<template>
  <div class="flex items-center space-x-3">
    <router-link v-if="clickable" :to="'/'">
      <img
        :src="logoSrc"
        alt="EmailConnect.eu logo"
        :class="`${sizeClasses[props.size]} w-auto`"
      />
    </router-link>
    <img
      v-else
      :src="logoSrc"
      alt="EmailConnect.eu logo"
      :class="`${sizeClasses[props.size]} w-auto`"
    />
  </div>
</template>
